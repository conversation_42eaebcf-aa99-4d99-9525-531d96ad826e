/// شاشة إدارة المستخدمين والصلاحيات
/// تدير المستخدمين وأدوارهم وصلاحياتهم في النظام
library;

import 'package:flutter/material.dart';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import '../models/user.dart';
import '../models/user_role.dart';
import '../services/user_service.dart';
import '../services/role_service.dart';
import '../constants/revolutionary_design_colors.dart';
import '../widgets/loading_widget.dart';
import '../responsive/responsive.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen>
    with TickerProviderStateMixin {
  final UserService _userService = UserService();
  final RoleService _roleService = RoleService();

  late TabController _tabController;
  List<User> _users = [];
  List<UserRole> _roles = [];
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    try {
      final users = await _userService.getAllUsers();
      final rolesData = await _roleService.getAllRoles();

      // تحويل Role إلى UserRole
      final roles = rolesData
          .map(
            (role) => UserRole(
              id: role.id,
              name: role.name,
              description: role.description,
              isActive: role.isActive,
              permissions: role.permissions
                  .map((permission) => permission.toString().split('.').last)
                  .toList(),
              createdAt: role.createdAt,
              updatedAt: role.updatedAt,
              notes: null, // Role model doesn't have notes field
            ),
          )
          .toList();

      setState(() {
        _users = users;
        _roles = roles;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('خطأ في تحميل البيانات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveText.h2('إدارة المستخدمين'),
        backgroundColor: RevolutionaryColors.damascusSky,
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(icon: Icon(Icons.people), text: 'المستخدمين'),
            Tab(icon: Icon(Icons.admin_panel_settings), text: 'الأدوار'),
            Tab(icon: Icon(Icons.security), text: 'الصلاحيات'),
          ],
        ),
      ),
      body: _isLoading
          ? const LoadingWidget()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildUsersTab(),
                _buildRolesTab(),
                _buildPermissionsTab(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showAddUserDialog,
        backgroundColor: RevolutionaryColors.damascusSky,
        icon: const Icon(Icons.person_add, color: Colors.white),
        label: const Text(
          'إضافة مستخدم',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildUsersTab() {
    final filteredUsers = _users
        .where(
          (user) =>
              user.username.toLowerCase().contains(
                _searchQuery.toLowerCase(),
              ) ||
              user.fullName.toLowerCase().contains(_searchQuery.toLowerCase()),
        )
        .toList();

    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            decoration: InputDecoration(
              hintText: 'البحث في المستخدمين...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              filled: true,
              fillColor: Colors.grey[50],
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
        ),

        // قائمة المستخدمين
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: filteredUsers.length,
            itemBuilder: (context, index) {
              final user = filteredUsers[index];
              return _buildUserCard(user);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUserCard(User user) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          backgroundColor: user.isActive
              ? RevolutionaryColors.damascusSky
              : Colors.grey,
          child: Text(
            user.fullName.isNotEmpty ? user.fullName[0].toUpperCase() : 'U',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          user.fullName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('اسم المستخدم: ${user.username}'),
            Text('الدور: ${user.roleName ?? 'غير محدد'}'),
            Text('آخر دخول: ${_formatDateTime(user.lastLoginAt)}'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر الحالة
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: user.isActive ? Colors.green : Colors.red,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              onSelected: (value) => _handleUserAction(value, user),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'permissions',
                  child: ListTile(
                    leading: Icon(Icons.security),
                    title: Text('الصلاحيات'),
                  ),
                ),
                PopupMenuItem(
                  value: user.isActive ? 'deactivate' : 'activate',
                  child: ListTile(
                    leading: Icon(
                      user.isActive ? Icons.block : Icons.check_circle,
                    ),
                    title: Text(user.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('حذف', style: TextStyle(color: Colors.red)),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRolesTab() {
    return Column(
      children: [
        // إحصائيات الأدوار
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الأدوار',
                  _roles.length.toString(),
                  Icons.admin_panel_settings,
                  RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'الأدوار النشطة',
                  _roles.where((r) => r.isActive).length.toString(),
                  Icons.check_circle,
                  RevolutionaryColors.successGlow,
                ),
              ),
            ],
          ),
        ),

        // قائمة الأدوار
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: _roles.length,
            itemBuilder: (context, index) {
              final role = _roles[index];
              return _buildRoleCard(role);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildRoleCard(UserRole role) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: Icon(
          Icons.admin_panel_settings,
          color: role.isActive ? RevolutionaryColors.damascusSky : Colors.grey,
        ),
        title: Text(
          role.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(role.description),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الصلاحيات:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.damascusSky,
                  ),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: role.permissions.map((permission) {
                    return Chip(
                      label: Text(
                        permission,
                        style: const TextStyle(fontSize: 12),
                      ),
                      backgroundColor: RevolutionaryColors.damascusSky
                          .withValues(alpha: 0.1),
                    );
                  }).toList(),
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => _editRole(role),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed: () => _deleteRole(role),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text(
                        'حذف',
                        style: TextStyle(color: Colors.red),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionsTab() {
    return const Center(
      child: Text(
        'إدارة الصلاحيات\nقريباً...',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.8)),
          ),
        ],
      ),
    );
  }

  void _handleUserAction(String action, User user) {
    switch (action) {
      case 'edit':
        _editUser(user);
        break;
      case 'permissions':
        _manageUserPermissions(user);
        break;
      case 'activate':
      case 'deactivate':
        _toggleUserStatus(user);
        break;
      case 'delete':
        _deleteUser(user);
        break;
    }
  }

  void _showAddUserDialog() {
    showDialog(
      context: context,
      builder: (context) => _AddUserDialog(
        roles: _roles,
        onUserAdded: () {
          _loadData(); // إعادة تحميل البيانات
        },
      ),
    );
  }

  void _editUser(User user) {
    showDialog(
      context: context,
      builder: (context) => _EditUserDialog(
        user: user,
        roles: _roles,
        onUserUpdated: () {
          _loadData(); // إعادة تحميل البيانات
        },
      ),
    );
  }

  void _manageUserPermissions(User user) {
    showDialog(
      context: context,
      builder: (context) => _UserPermissionsDialog(
        user: user,
        roles: _roles,
        onPermissionsUpdated: () {
          _loadData(); // إعادة تحميل البيانات
        },
      ),
    );
  }

  void _toggleUserStatus(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              user.isActive ? Icons.block : Icons.check_circle,
              color: user.isActive
                  ? RevolutionaryColors.errorCoral
                  : RevolutionaryColors.successGlow,
            ),
            const SizedBox(width: 8),
            Text(user.isActive ? 'إلغاء تفعيل المستخدم' : 'تفعيل المستخدم'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من ${user.isActive ? 'إلغاء تفعيل' : 'تفعيل'} المستخدم؟',
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المستخدم: ${user.fullName}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('اسم المستخدم: ${user.username}'),
                  Text('الدور: ${user.roleName ?? 'غير محدد'}'),
                ],
              ),
            ),
            if (user.isActive) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: RevolutionaryColors.warningAmber.withValues(
                    alpha: 0.1,
                  ),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: RevolutionaryColors.warningAmber.withValues(
                      alpha: 0.3,
                    ),
                  ),
                ),
                child: const Text(
                  '⚠️ تحذير: إلغاء تفعيل المستخدم سيمنعه من تسجيل الدخول إلى النظام.',
                  style: TextStyle(fontSize: 12),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performToggleUserStatus(user);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: user.isActive
                  ? RevolutionaryColors.errorCoral
                  : RevolutionaryColors.successGlow,
              foregroundColor: Colors.white,
            ),
            child: Text(user.isActive ? 'إلغاء التفعيل' : 'تفعيل'),
          ),
        ],
      ),
    );
  }

  Future<void> _performToggleUserStatus(User user) async {
    try {
      final success = await _userService.toggleUserStatus(user.id!);
      if (success) {
        _loadData(); // إعادة تحميل البيانات
        _showSuccessSnackBar(
          user.isActive
              ? 'تم إلغاء تفعيل المستخدم بنجاح'
              : 'تم تفعيل المستخدم بنجاح',
        );
      } else {
        _showErrorSnackBar('فشل في تغيير حالة المستخدم');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تغيير حالة المستخدم: $e');
    }
  }

  void _deleteUser(User user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.delete_forever, color: RevolutionaryColors.errorCoral),
            const SizedBox(width: 8),
            const Text('حذف المستخدم'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('هل أنت متأكد من حذف هذا المستخدم نهائياً؟'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'المستخدم: ${user.fullName}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('اسم المستخدم: ${user.username}'),
                  Text('البريد الإلكتروني: ${user.email}'),
                  Text('الدور: ${user.roleName ?? 'غير محدد'}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: RevolutionaryColors.errorCoral.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: RevolutionaryColors.errorCoral.withValues(alpha: 0.3),
                ),
              ),
              child: const Text(
                '⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات المستخدم نهائياً.',
                style: TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _performDeleteUser(user);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: RevolutionaryColors.errorCoral,
              foregroundColor: Colors.white,
            ),
            child: const Text('حذف نهائي'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDeleteUser(User user) async {
    try {
      final result = await _userService.deleteUser(user.id!);
      if (result > 0) {
        _loadData(); // إعادة تحميل البيانات
        _showSuccessSnackBar('تم حذف المستخدم بنجاح');
      } else {
        _showErrorSnackBar('فشل في حذف المستخدم');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في حذف المستخدم: $e');
    }
  }

  void _editRole(UserRole role) {
    // TODO: تنفيذ تعديل الدور
    _showInfoSnackBar('ميزة تعديل الأدوار قيد التطوير');
  }

  void _deleteRole(UserRole role) {
    // TODO: تنفيذ حذف الدور
    _showInfoSnackBar('ميزة حذف الأدوار قيد التطوير');
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return 'لم يسجل دخول';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.damascusSky,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }
}

/// حوار إضافة مستخدم جديد
class _AddUserDialog extends StatefulWidget {
  final List<UserRole> roles;
  final VoidCallback onUserAdded;

  const _AddUserDialog({required this.roles, required this.onUserAdded});

  @override
  State<_AddUserDialog> createState() => _AddUserDialogState();
}

class _AddUserDialogState extends State<_AddUserDialog> {
  final _formKey = GlobalKey<FormState>();
  final UserService _userService = UserService();

  // Controllers للحقول
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _fullNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _notesController = TextEditingController();

  // متغيرات الحالة
  bool _isLoading = false;
  bool _isActive = true;
  bool _isAdmin = false;
  bool _showPassword = false;
  bool _showConfirmPassword = false;
  UserRole? _selectedRole;

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildPasswordSection(),
                      const SizedBox(height: 24),
                      _buildContactInfoSection(),
                      const SizedBox(height: 24),
                      _buildRoleAndPermissionsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.person_add,
          color: RevolutionaryColors.damascusSky,
          size: 28,
        ),
        const SizedBox(width: 12),
        Text(
          'إضافة مستخدم جديد',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          tooltip: 'إغلاق',
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _usernameController,
                decoration: InputDecoration(
                  labelText: 'اسم المستخدم *',
                  hintText: 'أدخل اسم المستخدم',
                  prefixIcon: const Icon(Icons.person),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم المستخدم مطلوب';
                  }
                  if (value.length < 3) {
                    return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                  }
                  if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
                    return 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _fullNameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل *',
                  hintText: 'أدخل الاسم الكامل',
                  prefixIcon: const Icon(Icons.badge),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الاسم الكامل مطلوب';
                  }
                  if (value.length < 2) {
                    return 'الاسم الكامل يجب أن يكون حرفين على الأقل';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'كلمة المرور',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _passwordController,
                obscureText: !_showPassword,
                decoration: InputDecoration(
                  labelText: 'كلمة المرور *',
                  hintText: 'أدخل كلمة مرور قوية',
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showPassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _showPassword = !_showPassword;
                      });
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'كلمة المرور مطلوبة';
                  }
                  if (value.length < 8) {
                    return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                  }
                  if (!RegExp(
                    r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)',
                  ).hasMatch(value)) {
                    return 'كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _confirmPasswordController,
                obscureText: !_showConfirmPassword,
                decoration: InputDecoration(
                  labelText: 'تأكيد كلمة المرور *',
                  hintText: 'أعد إدخال كلمة المرور',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _showConfirmPassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _showConfirmPassword = !_showConfirmPassword;
                      });
                    },
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'تأكيد كلمة المرور مطلوب';
                  }
                  if (value != _passwordController.text) {
                    return 'كلمات المرور غير متطابقة';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'متطلبات كلمة المرور:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.damascusSky,
                  fontSize: 12,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '• 8 أحرف على الأقل\n• حرف كبير واحد على الأقل\n• حرف صغير واحد على الأقل\n• رقم واحد على الأقل',
                style: TextStyle(
                  fontSize: 11,
                  color: RevolutionaryColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الاتصال',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  labelText: 'البريد الإلكتروني *',
                  hintText: '<EMAIL>',
                  prefixIcon: const Icon(Icons.email),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'البريد الإلكتروني مطلوب';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'البريد الإلكتروني غير صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _phoneController,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: '+963 XXX XXX XXX',
                  prefixIcon: const Icon(Icons.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(r'^\+?[0-9\s\-\(\)]{10,15}$').hasMatch(value)) {
                      return 'رقم الهاتف غير صحيح';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleAndPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأدوار والصلاحيات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),

        // اختيار الدور
        DropdownButtonFormField<UserRole>(
          value: _selectedRole,
          decoration: InputDecoration(
            labelText: 'الدور *',
            hintText: 'اختر دور المستخدم',
            prefixIcon: const Icon(Icons.admin_panel_settings),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          items: widget.roles.where((role) => role.isActive).map((role) {
            return DropdownMenuItem<UserRole>(
              value: role,
              child: Text(role.name),
            );
          }).toList(),
          onChanged: (UserRole? value) {
            setState(() {
              _selectedRole = value;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'يجب اختيار دور للمستخدم';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // عرض صلاحيات الدور المختار
        if (_selectedRole != null) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: RevolutionaryColors.damascusSky.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'صلاحيات الدور: ${_selectedRole!.name}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: RevolutionaryColors.damascusSky,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _selectedRole!.description,
                  style: TextStyle(
                    color: RevolutionaryColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 4,
                  children: _selectedRole!.permissions.map((permission) {
                    return Chip(
                      label: Text(
                        permission,
                        style: const TextStyle(fontSize: 11),
                      ),
                      backgroundColor: RevolutionaryColors.damascusSky
                          .withValues(alpha: 0.1),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // خيارات إضافية
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: const Text('مستخدم نشط'),
                subtitle: const Text('يمكن للمستخدم تسجيل الدخول'),
                value: _isActive,
                onChanged: (bool? value) {
                  setState(() {
                    _isActive = value ?? true;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: const Text('مدير النظام'),
                subtitle: const Text('صلاحيات إدارية كاملة'),
                value: _isAdmin,
                onChanged: (bool? value) {
                  setState(() {
                    _isAdmin = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملاحظات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'ملاحظات إضافية',
            hintText: 'أدخل أي ملاحظات حول المستخدم...',
            prefixIcon: const Icon(Icons.note),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: _isLoading ? null : _saveUser,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('حفظ المستخدم'),
        ),
      ],
    );
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // تشفير كلمة المرور
      final hashedPassword = _hashPassword(_passwordController.text);

      // إنشاء كائن المستخدم الجديد
      final newUser = User(
        username: _usernameController.text.trim(),
        passwordHash: hashedPassword,
        fullName: _fullNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        isActive: _isActive,
        isAdmin: _isAdmin,
        roleId: _selectedRole?.id,
        roleName: _selectedRole?.name,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      // حفظ المستخدم في قاعدة البيانات
      await _userService.insertUser(newUser);

      if (mounted) {
        Navigator.of(context).pop();
        widget.onUserAdded();
        _showSuccessSnackBar('تم إضافة المستخدم بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في إضافة المستخدم: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _hashPassword(String password) {
    // استخدام نفس طريقة التشفير المستخدمة في AuthService
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }
}

/// حوار تعديل مستخدم موجود
class _EditUserDialog extends StatefulWidget {
  final User user;
  final List<UserRole> roles;
  final VoidCallback onUserUpdated;

  const _EditUserDialog({
    required this.user,
    required this.roles,
    required this.onUserUpdated,
  });

  @override
  State<_EditUserDialog> createState() => _EditUserDialogState();
}

class _EditUserDialogState extends State<_EditUserDialog> {
  final _formKey = GlobalKey<FormState>();
  final UserService _userService = UserService();

  // Controllers للحقول
  late final TextEditingController _usernameController;
  late final TextEditingController _fullNameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _notesController;
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  // متغيرات الحالة
  bool _isLoading = false;
  late bool _isActive;
  late bool _isAdmin;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;
  bool _changePassword = false;
  UserRole? _selectedRole;

  @override
  void initState() {
    super.initState();

    // تهيئة Controllers بالبيانات الحالية
    _usernameController = TextEditingController(text: widget.user.username);
    _fullNameController = TextEditingController(text: widget.user.fullName);
    _emailController = TextEditingController(text: widget.user.email);
    _phoneController = TextEditingController(text: widget.user.phone ?? '');
    _notesController = TextEditingController(text: widget.user.notes ?? '');

    // تهيئة متغيرات الحالة
    _isActive = widget.user.isActive;
    _isAdmin = widget.user.isAdmin;

    // العثور على الدور الحالي
    _selectedRole = widget.roles.firstWhere(
      (role) => role.id == widget.user.roleId,
      orElse: () => widget.roles.first,
    );
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _fullNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _notesController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.85,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildPasswordSection(),
                      const SizedBox(height: 24),
                      _buildContactInfoSection(),
                      const SizedBox(height: 24),
                      _buildRoleAndPermissionsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.edit, color: RevolutionaryColors.damascusSky, size: 28),
        const SizedBox(width: 12),
        Text(
          'تعديل المستخدم: ${widget.user.fullName}',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          tooltip: 'إغلاق',
        ),
      ],
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'المعلومات الأساسية',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _usernameController,
                decoration: InputDecoration(
                  labelText: 'اسم المستخدم *',
                  hintText: 'أدخل اسم المستخدم',
                  prefixIcon: const Icon(Icons.person),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'اسم المستخدم مطلوب';
                  }
                  if (value.length < 3) {
                    return 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
                  }
                  if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(value)) {
                    return 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _fullNameController,
                decoration: InputDecoration(
                  labelText: 'الاسم الكامل *',
                  hintText: 'أدخل الاسم الكامل',
                  prefixIcon: const Icon(Icons.badge),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'الاسم الكامل مطلوب';
                  }
                  if (value.length < 2) {
                    return 'الاسم الكامل يجب أن يكون حرفين على الأقل';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'كلمة المرور',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),

        // خيار تغيير كلمة المرور
        CheckboxListTile(
          title: const Text('تغيير كلمة المرور'),
          subtitle: const Text('اتركه فارغاً للاحتفاظ بكلمة المرور الحالية'),
          value: _changePassword,
          onChanged: (bool? value) {
            setState(() {
              _changePassword = value ?? false;
              if (!_changePassword) {
                _newPasswordController.clear();
                _confirmPasswordController.clear();
              }
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),

        if (_changePassword) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _newPasswordController,
                  obscureText: !_showNewPassword,
                  decoration: InputDecoration(
                    labelText: 'كلمة المرور الجديدة *',
                    hintText: 'أدخل كلمة مرور جديدة',
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showNewPassword
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _showNewPassword = !_showNewPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  validator: _changePassword
                      ? (value) {
                          if (value == null || value.isEmpty) {
                            return 'كلمة المرور الجديدة مطلوبة';
                          }
                          if (value.length < 8) {
                            return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                          }
                          if (!RegExp(
                            r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)',
                          ).hasMatch(value)) {
                            return 'كلمة المرور يجب أن تحتوي على أحرف كبيرة وصغيرة وأرقام';
                          }
                          return null;
                        }
                      : null,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextFormField(
                  controller: _confirmPasswordController,
                  obscureText: !_showConfirmPassword,
                  decoration: InputDecoration(
                    labelText: 'تأكيد كلمة المرور *',
                    hintText: 'أعد إدخال كلمة المرور',
                    prefixIcon: const Icon(Icons.lock_outline),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showConfirmPassword
                            ? Icons.visibility_off
                            : Icons.visibility,
                      ),
                      onPressed: () {
                        setState(() {
                          _showConfirmPassword = !_showConfirmPassword;
                        });
                      },
                    ),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  validator: _changePassword
                      ? (value) {
                          if (value == null || value.isEmpty) {
                            return 'تأكيد كلمة المرور مطلوب';
                          }
                          if (value != _newPasswordController.text) {
                            return 'كلمات المرور غير متطابقة';
                          }
                          return null;
                        }
                      : null,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildContactInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'معلومات الاتصال',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _emailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  labelText: 'البريد الإلكتروني *',
                  hintText: '<EMAIL>',
                  prefixIcon: const Icon(Icons.email),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'البريد الإلكتروني مطلوب';
                  }
                  if (!RegExp(
                    r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$',
                  ).hasMatch(value)) {
                    return 'البريد الإلكتروني غير صحيح';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _phoneController,
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  labelText: 'رقم الهاتف',
                  hintText: '+963 XXX XXX XXX',
                  prefixIcon: const Icon(Icons.phone),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(r'^\+?[0-9\s\-\(\)]{10,15}$').hasMatch(value)) {
                      return 'رقم الهاتف غير صحيح';
                    }
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRoleAndPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأدوار والصلاحيات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),

        // اختيار الدور
        DropdownButtonFormField<UserRole>(
          value: _selectedRole,
          decoration: InputDecoration(
            labelText: 'الدور *',
            hintText: 'اختر دور المستخدم',
            prefixIcon: const Icon(Icons.admin_panel_settings),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          items: widget.roles.where((role) => role.isActive).map((role) {
            return DropdownMenuItem<UserRole>(
              value: role,
              child: Text(role.name),
            );
          }).toList(),
          onChanged: (UserRole? value) {
            setState(() {
              _selectedRole = value;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'يجب اختيار دور للمستخدم';
            }
            return null;
          },
        ),

        const SizedBox(height: 16),

        // خيارات إضافية
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: const Text('مستخدم نشط'),
                subtitle: const Text('يمكن للمستخدم تسجيل الدخول'),
                value: _isActive,
                onChanged: (bool? value) {
                  setState(() {
                    _isActive = value ?? true;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: const Text('مدير النظام'),
                subtitle: const Text('صلاحيات إدارية كاملة'),
                value: _isAdmin,
                onChanged: (bool? value) {
                  setState(() {
                    _isAdmin = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'ملاحظات',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            labelText: 'ملاحظات إضافية',
            hintText: 'أدخل أي ملاحظات حول المستخدم...',
            prefixIcon: const Icon(Icons.note),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateUser,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('حفظ التغييرات'),
        ),
      ],
    );
  }

  Future<void> _updateUser() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      // إنشاء كائن المستخدم المحدث
      String passwordHash = widget.user.passwordHash;

      // تشفير كلمة المرور الجديدة إذا تم تغييرها
      if (_changePassword && _newPasswordController.text.isNotEmpty) {
        passwordHash = _hashPassword(_newPasswordController.text);
      }

      final updatedUser = widget.user.copyWith(
        username: _usernameController.text.trim(),
        passwordHash: passwordHash,
        fullName: _fullNameController.text.trim(),
        email: _emailController.text.trim(),
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        isActive: _isActive,
        isAdmin: _isAdmin,
        roleId: _selectedRole?.id,
        roleName: _selectedRole?.name,
        updatedAt: DateTime.now(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
      );

      // تحديث المستخدم في قاعدة البيانات
      await _userService.updateUser(updatedUser);

      if (mounted) {
        Navigator.of(context).pop();
        widget.onUserUpdated();
        _showSuccessSnackBar('تم تحديث المستخدم بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحديث المستخدم: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }
}

/// حوار إدارة صلاحيات المستخدم
class _UserPermissionsDialog extends StatefulWidget {
  final User user;
  final List<UserRole> roles;
  final VoidCallback onPermissionsUpdated;

  const _UserPermissionsDialog({
    required this.user,
    required this.roles,
    required this.onPermissionsUpdated,
  });

  @override
  State<_UserPermissionsDialog> createState() => _UserPermissionsDialogState();
}

class _UserPermissionsDialogState extends State<_UserPermissionsDialog> {
  final UserService _userService = UserService();
  final RoleService _roleService = RoleService();

  bool _isLoading = false;
  UserRole? _selectedRole;

  @override
  void initState() {
    super.initState();

    // العثور على الدور الحالي
    _selectedRole = widget.roles.firstWhere(
      (role) => role.id == widget.user.roleId,
      orElse: () => widget.roles.first,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildUserInfo(),
                    const SizedBox(height: 24),
                    _buildRoleSelection(),
                    const SizedBox(height: 24),
                    _buildPermissionsDisplay(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(Icons.security, color: RevolutionaryColors.damascusSky, size: 28),
        const SizedBox(width: 12),
        Text(
          'إدارة صلاحيات المستخدم',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close),
          tooltip: 'إغلاق',
        ),
      ],
    );
  }

  Widget _buildUserInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: RevolutionaryColors.damascusSky.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: RevolutionaryColors.damascusSky.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات المستخدم',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: RevolutionaryColors.damascusSky,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              CircleAvatar(
                backgroundColor: widget.user.isActive
                    ? RevolutionaryColors.damascusSky
                    : Colors.grey,
                child: Text(
                  widget.user.fullName.isNotEmpty
                      ? widget.user.fullName[0].toUpperCase()
                      : 'U',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.user.fullName,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text('اسم المستخدم: ${widget.user.username}'),
                    Text('البريد الإلكتروني: ${widget.user.email}'),
                    Text('الحالة: ${widget.user.statusText}'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoleSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تغيير الدور',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<UserRole>(
          value: _selectedRole,
          decoration: InputDecoration(
            labelText: 'الدور',
            hintText: 'اختر دور المستخدم',
            prefixIcon: const Icon(Icons.admin_panel_settings),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
          ),
          items: widget.roles.where((role) => role.isActive).map((role) {
            return DropdownMenuItem<UserRole>(
              value: role,
              child: Text(role.name),
            );
          }).toList(),
          onChanged: (UserRole? value) {
            setState(() {
              _selectedRole = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildPermissionsDisplay() {
    if (_selectedRole == null) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'صلاحيات الدور: ${_selectedRole!.name}',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: RevolutionaryColors.damascusSky,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          _selectedRole!.description,
          style: TextStyle(
            color: RevolutionaryColors.textSecondary,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: RevolutionaryColors.damascusSky.withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: RevolutionaryColors.damascusSky.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'الصلاحيات المتاحة:',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: RevolutionaryColors.damascusSky,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _selectedRole!.permissions.map((permission) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: RevolutionaryColors.damascusSky.withValues(
                        alpha: 0.1,
                      ),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: RevolutionaryColors.damascusSky.withValues(
                          alpha: 0.3,
                        ),
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: RevolutionaryColors.successGlow,
                        ),
                        const SizedBox(width: 4),
                        Text(permission, style: const TextStyle(fontSize: 12)),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateUserRole,
          style: ElevatedButton.styleFrom(
            backgroundColor: RevolutionaryColors.damascusSky,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('حفظ التغييرات'),
        ),
      ],
    );
  }

  Future<void> _updateUserRole() async {
    if (_selectedRole == null || _selectedRole!.id == widget.user.roleId) {
      Navigator.of(context).pop();
      return;
    }

    setState(() => _isLoading = true);

    try {
      // تحديث دور المستخدم
      final updatedUser = widget.user.copyWith(
        roleId: _selectedRole!.id,
        roleName: _selectedRole!.name,
        updatedAt: DateTime.now(),
      );

      await _userService.updateUser(updatedUser);

      if (mounted) {
        Navigator.of(context).pop();
        widget.onPermissionsUpdated();
        _showSuccessSnackBar('تم تحديث صلاحيات المستخدم بنجاح');
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('خطأ في تحديث صلاحيات المستخدم: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.successGlow,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: RevolutionaryColors.errorCoral,
      ),
    );
  }
}
